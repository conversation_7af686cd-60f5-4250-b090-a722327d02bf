from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from rest_framework.permissions import IsAuthenticated

from accounts.models import CustomUser
from accounts.serializers import CustomUserSerializer
from accounts.permissions import IsAdminUser


class AdminUserListCreateView(ListCreateAPIView):
    """Admin: List all users or create one"""
    queryset = CustomUser.objects.all()
    serializer_class = CustomUserSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]


class AdminUserDetailView(RetrieveUpdateDestroyAPIView):
    """Admin: Get, update, or delete user by ID"""
    queryset = CustomUser.objects.all()
    serializer_class = CustomUserSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
