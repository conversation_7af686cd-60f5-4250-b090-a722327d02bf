# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-29 17:30+0100\n"
"Last-Translator: Pasindu <<EMAIL>>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "Auktoriseringshuvudet måste innehålla två mellanslagsavgränsade värden"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Givet token är inte giltigt för någon tokentyp"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token innehöll ingen identifiering av användaren"

#: authentication.py:132
msgid "User not found"
msgstr "Användaren hittades inte"

#: authentication.py:135
msgid "User is inactive"
msgstr "Användaren är inaktiv"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Okänd algoritmtyp '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Du måste ha kryptografi installerad för att kunna använda {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token är ogiltig eller har löpt ut"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Ogiltig algoritm har angetts"

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token är ogiltig eller har löpt ut"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token är ogiltig eller har löpt ut"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Inget aktivt konto hittades med de angivna användaruppgifterna"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Inget aktivt konto hittades med de angivna användaruppgifterna"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Inställningen '{}' har tagits bort. Se '{}' för tillgängliga inställningar"

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "användare"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "skapad vid"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "går ut kl"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token svartlist"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Kan inte skapa token utan typ eller livslängd"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token har inget id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token har ingen typ"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token har fel typ"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token har inget '{}'-anspråk"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Token '{}'-anspråket har löpt ut"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token är svartlistad"
