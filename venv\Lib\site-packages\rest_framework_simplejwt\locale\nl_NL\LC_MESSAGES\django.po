# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-06-17 11:06+0200\n"
"Last-Translator: rene <<EMAIL>>\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Authorisatie header moet twee waarden bevatten, gescheiden door een spatie"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Het token is voor geen enkel token-type geldig"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token bevat geen herkenbare gebruikersidentificatie"

#: authentication.py:132
msgid "User not found"
msgstr "Gebruiker niet gevonden"

#: authentication.py:135
msgid "User is inactive"
msgstr "Gebruiker is inactief"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Niet herkend algoritme type '{}"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:68
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token is niet geldig of verlopen"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:66
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token is niet geldig of verlopen"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token is niet geldig of verlopen"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Geen actief account gevonden voor deze gegevens"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Geen actief account gevonden voor deze gegevens"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"De '{}' instelling bestaat niet meer. Zie '{}' for beschikbareinstellingen."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "gebruiker"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "aangemaakt op"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "verloopt op"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:52
msgid "Cannot create token with no type or lifetime"
msgstr "Kan geen token maken zonder type of levensduur"

#: tokens.py:126
msgid "Token has no id"
msgstr "Token heeft geen id"

#: tokens.py:138
msgid "Token has no type"
msgstr "Token heeft geen type"

#: tokens.py:141
msgid "Token has wrong type"
msgstr "Token heeft het verkeerde type"

#: tokens.py:200
msgid "Token has no '{}' claim"
msgstr "Token heeft geen '{}' recht"

#: tokens.py:205
msgid "Token '{}' claim has expired"
msgstr "Token '{}' recht is verlopen"

#: tokens.py:292
msgid "Token is blacklisted"
msgstr "Token is ge-blacklist"
